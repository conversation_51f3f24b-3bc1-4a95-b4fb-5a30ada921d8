import clsx from 'clsx';
import {CSSProperties, useMemo} from 'react';
import {Panel, PanelGroup, PanelResizeHandle} from 'react-resizable-panels';
import {useParams} from 'react-router';
import {useWindowScroll, useDebouncedState} from '@mantine/hooks';
import {ColDef, iconSetMaterial, themeQuartz} from 'ag-grid-enterprise';
import {AgGridReact} from 'ag-grid-react';
import {format, parse} from 'date-fns';
import {observer} from 'mobx-react-lite';
import {useGlobalStoreLoader} from '@/store/global.store.ts';
import TrendViewGraph from '@/views/patients/patient-detail/patient-trend-view/components/trend-view-graph.tsx';
import useComputeRftTrendData from '@/views/patients/patient-detail/patient-trend-view/use-compute-rft-trend-data.ts';

export interface ParameterSelection {
  name: string;
  id: string;
  values: (number | undefined)[];
  dates: string[];
  actualTestDates: (string | undefined)[];
  percentages: (number | undefined)[];
  timestamps: number[];
}

const selectionDisabledAttr = [
  'Date',
  'Time',
  'Test Type',
  'Pre-condition',
  'Post Condition',
  'Report',
  'Technical Note',
];

const processAllDataPoints = (stores: any[], header: any) => {
  const dataPoints: {
    value: number | undefined;
    actualTestDate: string;
    timestamp: number;
  }[] = [];

  stores.forEach((store) => {
    if (store.test.testdate) {
      const value = header?.render && header.render(store);
      const actualTestDate = store.test.testtime
        ? `${store.test.testdate} ${store.test.testtime.split('.')[0]}`
        : store.test.testdate;
      const timestamp = store.test.testtime
        ? new Date(`${store.test.testdate} ${store.test.testtime.split('.')[0]}`).getTime()
        : new Date(store.test.testdate).getTime();

      dataPoints.push({
        value: value !== undefined && value !== null && value !== '-' ? (value as any) : undefined,
        actualTestDate,
        timestamp,
      });
    }
  });


  const values = dataPoints.map((point) => point.value);
  const actualTestDates = dataPoints.map((point) => point.actualTestDate);
  const timestamps = dataPoints.map((point) => point.timestamp);
  const earliestRefValue = values.find((value) => value !== undefined);
  const percentages = values.map((value) =>
    value !== undefined && earliestRefValue !== undefined
      ? Math.round((value / earliestRefValue) * 100)
      : undefined
  );

  return {values, percentages, actualTestDates, timestamps};
};

const PatientTrendTable = observer(() => {
  useGlobalStoreLoader();
  const {patientId} = useParams();
  const [scroll] = useWindowScroll();
  const [parameterSelection, setParameterSelection] = useDebouncedState<ParameterSelection[] | []>([], 300);

  const {stores, isLoading, rftHeaders} = useComputeRftTrendData({patientId: patientId ?? ''});

  const filteredHeaders = useMemo(() => {
    return rftHeaders.filter((header) => !header.id.includes('Test Type'));
  }, [rftHeaders]);

  const earliestToLatestStores = useMemo(() => {
    return [...stores].sort((a, b) => {
      const aDateTime = a.test.testtime
        ? new Date(`${a.test.testdate} ${a.test.testtime.split('.')[0]}`)
        : new Date(a.test.testdate);
      const bDateTime = b.test.testtime
        ? new Date(`${b.test.testdate} ${b.test.testtime.split('.')[0]}`)
        : new Date(b.test.testdate);
      return aDateTime.getTime() - bDateTime.getTime();
    });
  }, [stores]);

  const dataPointCache = useMemo(() => {
    const cache: Record<string, ReturnType<typeof processAllDataPoints>> = {};
    return (header: any) => {
      if (!cache[header.id]) {
        cache[header.id] = processAllDataPoints(earliestToLatestStores, header);
      }
      return cache[header.id];
    };
  }, [earliestToLatestStores]);

  const valueGetter = useMemo(
    () => (params: any) => {
      if (params.node.group) return '';
      const header = filteredHeaders[params?.data?.rowIndex];
      if (!header) {
        console.warn(`No header found for rowIndex: ${params?.data?.rowIndex}`);
        return '';
      }
      if (!header.render) {
        console.warn(`No render function for header: ${header.name}`);
        return '';
      }
      const value = header.render(params?.data[params.colDef.field]);
      return value ?? '-';
    },
    [filteredHeaders]
  );

  const cellRenderer = useMemo(
    () => (params: any) => {
      return params.value ? params.value : '-';
    },
    []
  );

  const columnDefs = useMemo<ColDef[]>(() => {
    const testTypeColumn: ColDef = {
      field: 'testType',
      headerName: 'Test Type',
      hide: true,
      rowGroup: true,
      suppressMovable: true,
    };

    const headerColumn: ColDef = {
      headerName: 'PARAMETER',
      field: 'rowHeader',
      pinned: 'left',
      suppressHeaderMenuButton: true,
      width: 232,
      minWidth: 180,
      cellRenderer: (params: any) => {
        const header = filteredHeaders[params?.data?.rowIndex];
        if (!header) return '';
        const ParameterUnit = header?.unit && header?.unit(params?.data[params.colDef.field]);
        return (
          <div
            className={clsx('flex items-center font-semibold uppercase', {
              'pl-6': params.node.level > 0,
            })}
          >
            {header.name}
            {ParameterUnit && <span className="ml-1 text-neutral-500 normal-case">[{ParameterUnit}]</span>}
          </div>
        );
      },
      suppressMovable: true,
      rowGroup: false,
      checkboxSelection: (params: any) => {
        if (params.node.group) return false;
        const header = filteredHeaders[params?.data?.rowIndex];
        return header && !selectionDisabledAttr.includes(header.name);
      },
    };

    const dataColumns: ColDef[] = stores.map((store, index) => ({
      headerName: store.test.testdate
        ? store.test.testtime
          ? format(
              parse(
                `${store.test.testdate} ${store.test.testtime.split('.')[0]}`,
                'yyyy-MM-dd HH:mm:ss',
                new Date()
              ),
              'dd MMM yyyy HH:mm'
            )
          : format(parse(store.test.testdate, 'yyyy-MM-dd', new Date()), 'dd MMM yyyy')
        : `Session ${index + 1}`,
      field: `session_${(store.test as any).sessionid}_${store.test.testdate}_${store.test.testtime}`,
      suppressHeaderMenuButton: true,
      minWidth: 135,
      width: 141,
      valueGetter,
      tooltipValueGetter: (params: any) =>
        params.value !== '-' && params?.value?.length > 20 ? params.value : null,
      cellRenderer,
    }));

    return [testTypeColumn, headerColumn, ...dataColumns];
  }, [stores, filteredHeaders, valueGetter, cellRenderer]);

  const rowData = useMemo(() => {
    const validRows = filteredHeaders
      .map((header, index) => {
        const row = {
          rowIndex: index,
          rowHeader: header.name,
          testType: header.testType || 'General',
          ...stores.reduce(
            (acc, store) => {
              const dateTime = `${store.test.testdate}_${store.test.testtime}`
              acc[`session_${(store?.test as any)?.sessionid}_${dateTime}`] = store;
              return acc;
            },
            {} as Record<string, any>
          ),
        };

        const hasValidData = stores.some((store) => {
          const value = header.render ? header.render(store) : undefined;
          return value !== undefined && value !== null && value !== '-';
        });

        return hasValidData ? row : null;
      })
      .filter((row) => row !== null);

    const validTestTypes = new Set(validRows.map((row) => row!.testType));
    return validRows.filter((row) => validTestTypes.has(row!.testType)) as any[];
  }, [filteredHeaders, stores]);

  const myTheme = useMemo(
    () =>
      themeQuartz.withPart(iconSetMaterial).withParams({
        fontFamily: 'Figtree, sans-serif',
        fontSize: 'var(--text-sm)',
        rowHoverColor: 'color-mix(in oklab, var(--color-muted) 50%, transparent)',
        backgroundColor: 'var(--color-white)',
        columnBorder: false,
        headerFontSize: 'var(--text-xs)',
        headerFontWeight: 'bold',
        headerTextColor: 'var(--color-neutral-700)',
        headerHeight: 48,
        iconSize: 13,
        rowHeight: 32,
        headerBackgroundColor: 'var(--color-white)',
        borderColor: 'var(--color-neutral-200)',
        textColor: 'var(--color-neutral-700)',
        rangeSelectionBorderColor: 'var(--color-brand2-500)',
        rangeHeaderHighlightColor: 'var(--color-brand2-500)',
        accentColor: 'var(--color-brand-500)',
        rowBorder: true,
        selectCellBorder: true,
        wrapperBorder: true,
        borderRadius: 0,
        cellEditingShadow: false,
        selectCellBackgroundColor: 'var(--color-white)',
        cellHorizontalPadding: 8,
        selectedRowBackgroundColor: 'var(--color-brand-50)',
        checkboxCheckedBackgroundColor: 'var(--color-brand-500)',
        checkboxCheckedBorderColor: 'var(--color-brand-500)',
        checkboxBorderRadius: '2px',
      }),
    []
  );

  return (
    <>
      {rowData.length > 0 ? (
        <div
          className="sticky top-11.5 isolate z-30 w-full overflow-y-auto overscroll-contain"
          style={
            {
              height: `min(100vh - ${Math.max(206 - scroll.y, 0)}px, 100vh - 66px)`,
            } as CSSProperties
          }
        >
          <PanelGroup direction="vertical" className="h-full">
            <Panel defaultSize={parameterSelection.length ? 60 : 100} minSize={30}>
              <div className="h-full w-full">
                <AgGridReact
                  className="compact-ag-grid h-full w-full"
                  columnDefs={columnDefs}
                  rowData={rowData}
                  theme={myTheme}
                  loading={isLoading}
                  getRowId={(params) => String(params.data.rowIndex)}
                  animateRows={true}
                  pagination={false}
                  suppressRowVirtualisation={false}
                  suppressContextMenu={true}
                  suppressColumnVirtualisation={true}
                  groupDisplayType="groupRows"
                  groupDefaultExpanded={1}
                  rowSelection="multiple"
                  suppressRowClickSelection={true}
                  onSelectionChanged={(event) => {
                    const selectedRows = event.api.getSelectedRows();
                    const selectedKeys = selectedRows.map((row) => filteredHeaders[row.rowIndex].id);

                    const selectedParameters = filteredHeaders
                      .filter((header) => selectedKeys.includes(header.id))
                      .map((header) => {
                        const {values, percentages, actualTestDates, timestamps} = dataPointCache(header);
                        const dates = timestamps.map((timestamp) => format(new Date(timestamp), 'yyyy-MM-dd'));
                        return {
                          name: header.name,
                          id: header.id,
                          dates,
                          actualTestDates,
                          values,
                          percentages,
                          timestamps,
                        };
                      });
                    setParameterSelection(selectedParameters);
                  }}
                />
              </div>
            </Panel>

            {parameterSelection.length > 0 && (
              <>
                <PanelResizeHandle className="flex h-1.5 cursor-row-resize items-center justify-center p-2">
                  <div className="my-0.5 h-1 w-10 rounded-full bg-neutral-400" />
                </PanelResizeHandle>
                <Panel defaultSize={40} minSize={40}>
                  <TrendViewGraph parameterSelection={parameterSelection} isLoading={isLoading} />
                </Panel>
              </>
            )}
          </PanelGroup>
        </div>
      ) : (
        <div className="flex h-150 flex-1 flex-col items-center justify-center space-y-2">
          <img src="/no-trend-selection.svg" alt="Empty Folder" className="h-32.5 w-32.5" />
          <div className="text-center">
            <div className="text-xl font-bold text-neutral-900 capitalize">no trend data to show</div>
            <div className="text-sm text-neutral-700 capitalize">
              no trend data available for this patient
            </div>
          </div>
        </div>
      )}
    </>
  );
});

export default PatientTrendTable;