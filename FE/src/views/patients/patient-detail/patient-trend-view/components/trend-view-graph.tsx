import clsx from 'clsx';
import {useEffect, useMemo, useState} from 'react';

import {TooltipTrigger} from '@radix-ui/react-tooltip';
import {eachMonthOfInterval, format, startOfMonth} from 'date-fns';
import {
  Brush,
  CartesianGrid,
  LabelList,
  Legend,
  Line,
  LineChart,
  ReferenceArea,
  XAxis,
  YAxis,
} from 'recharts';
import {CategoricalChartState} from 'recharts/types/chart/types';

import {Button} from '@/components/ui/button';
import {Card, CardContent, CardHeader, CardTitle} from '@/components/ui/card';
import {ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent} from '@/components/ui/chart';
import {Switch} from '@/components/ui/switch.tsx';
import {Tooltip, TooltipContent} from '@/components/ui/tooltip';
import {ParameterSelection} from '@/views/patients/patient-detail/patient-trend-view/components/patient-trend-table.tsx';

interface ParameterData {
  name: string;
  id: string;
  dates: string[];
  actualTestDates?: (string | undefined)[];
  values: (string | number | null)[];
  percentages: (string | number | null)[];
  timestamps: number[];
}

const parameterColors: Record<string, string> = {
  Weight: '#FF6B6B',
  BMI: '#4ECDC4',
  'Pre FEV1': '#45B7D1',
  'Pre FVC': '#96CEB4',
  'Pre VC': '#ffa923',
  'Pre FER (%)': '#D4A5A5',
  'Post FEV1': '#9B59B6',
  'Post FVC': '#3498DB',
  'Post VC': '#E67E22',
  'Post FER (%)': '#2ECC71',
  VA: '#F1C40F',
  TLCO: '#E74C3C',
  KCO: '#8E44AD',
  'TLCO Hb': '#16A085',
  'KCO Hb': '#D35400',
  TLC: '#2980B9',
  FRC: '#27AE60',
  RV: '#C0392B',
  'RV/TLC (%)': '#8D5524',
  MIP: '#00CED1',
  MEP: '#df90f4',
  SNIP: '#ff5800',
  FeNO: '#4682B4',
  'FEV1/FVC (%)': '#1ABC9C',
  'FEV1/VC (%)': '#F39C12',
  'FEF25-75': '#7F8C8D',
  PEF: '#34495E',
  Vi: '#8d6cc9',
  Hb: '#BDC3C7',
  'Vi/VC (%)': '#2C3E50',
  IC: '#95A5A6',
  ERV: '#D81B60',
  LvVC: '#8BC34A',
};

const generateMonthTicks = (timestamps: number[]) => {
  if (timestamps.length === 0) return [];

  const minTimestamp = Math.min(...timestamps);
  const maxTimestamp = Math.max(...timestamps);

  const startMonth = startOfMonth(new Date(minTimestamp));
  const endMonth = startOfMonth(new Date(maxTimestamp));

  const monthTicks = eachMonthOfInterval({
    start: startMonth,
    end: endMonth,
  }).map((date) => date.getTime());

  return monthTicks;
};

const transformData = (parameters: ParameterData[], type: 'absolute' | 'percentage') => {
  if (!parameters || parameters.length === 0) return [];

  const referenceTimestamps = parameters[0]?.timestamps || [];

  const transformed = referenceTimestamps.map((timestamp, index) => {
    const dataPoint: Record<string, number | null | string> = {
      date: format(new Date(timestamp), 'yyyy-MM-dd'),
      timestamp,
      actualTestDate: parameters[0]?.actualTestDates?.[index] || format(new Date(timestamp), 'yyyy-MM-dd'),
    };
    parameters.forEach((param) => {
      const value = type === 'percentage' ? param.percentages[index] : param.values[index];
      dataPoint[param.id] =
        value === '' || value === null || value === undefined || isNaN(Number(value)) ? null : Number(value);
    });
    return dataPoint;
  });

  return transformed;
};

const createChartConfig = (parameters: ParameterData[]): ChartConfig => {
  const config: ChartConfig = {};
  parameters.forEach((param) => {
    config[param.id] = {
      label: param.name,
      color: parameterColors[param.id] || '#000000',
    };
  });
  return config;
};

const getAxisYDomain = (
  data: any[],
  from: number | string,
  to: number | string,
  parameterIds: string[],
  offset: number = 5
) => {
  const filteredData = data.filter((d) => d.timestamp >= from && d.timestamp <= to);

  if (filteredData.length === 0) return ['dataMin', 'dataMax'];

  let bottom = Infinity;
  let top = -Infinity;

  filteredData.forEach((d) => {
    parameterIds.forEach((paramId) => {
      const value = d[paramId];
      if (value !== null && value !== undefined && typeof value === 'number') {
        if (value > top) top = value;
        if (value < bottom) bottom = value;
      }
    });
  });

  if (bottom === Infinity || top === -Infinity) {
    return ['dataMin', 'dataMax'];
  }

  return [Math.floor(bottom) - offset, Math.ceil(top) + offset];
};

const findDataIndices = (data: any[], startTimestamp: number | string, endTimestamp: number | string) => {
  if (startTimestamp === 'dataMin' || endTimestamp === 'dataMax') {
    return {
      startIndex: undefined,
      endIndex: undefined
    };
  }

  const startIndex = data.findIndex(item => item.timestamp >= startTimestamp);
  const endIndex = data.findLastIndex(item => item.timestamp <= endTimestamp);

  return {
    startIndex: startIndex >= 0 ? startIndex : undefined,
    endIndex: endIndex >= 0 ? endIndex : undefined
  };
};

interface LabelPositionInfo {
  yOffset: number;
  height: number;
}

const calculateLabelPosition = (
  values: (string | number | null)[],
  index: number,
  y: number,
  threshold: number = 5,
  labelHeight: number = 15
): LabelPositionInfo => {
  if (index === 0 || values[index] === null || typeof values[index] === 'string') {
    return {yOffset: -labelHeight, height: labelHeight};
  }

  const currentValue = Number(values[index]);
  const positions: LabelPositionInfo[] = [];

  for (let i = 0; i < index; i++) {
    if (values[i] === null || typeof values[i] === 'string') {
      positions.push({yOffset: -labelHeight, height: labelHeight});
    } else {
      positions.push(calculateLabelPosition(values, i, y, threshold, labelHeight));
    }
  }

  let baseOffset = -labelHeight;
  let stackCount = 0;
  for (let i = index - 1; i >= 0; i--) {
    const prevValue = Number(values[i]);
    if (Math.abs(currentValue - prevValue) <= threshold) {
      const prevPos = positions[i];
      const prevTop = y + prevPos.yOffset;
      const prevBottom = prevTop + prevPos.height;

      if (baseOffset + y >= prevTop && baseOffset + y <= prevBottom) {
        stackCount++;
        baseOffset = prevPos.yOffset - labelHeight * stackCount;
      }
    } else {
      break;
    }
  }

  return {yOffset: baseOffset, height: labelHeight};
};

const CustomLabel = (props: any) => {
  const {x, y, value, index, parameter, type} = props;

  if (value === null || typeof value === 'string') return null;

  const positionInfo = calculateLabelPosition(
    type === 'percentages' ? parameter.percentages : parameter.values,
    index,
    y
  );

  return (
    <text
      x={x}
      y={y + positionInfo.yOffset}
      fill={parameterColors[parameter.id] || '#000000'}
      textAnchor="middle"
      fontSize={12}
    >
      {type === 'percentage' ? `${value}%` : value}
    </text>
  );
};

function EmptyState({title, description}: {title: string; description: string}) {
  return (
    <div className="table-empty min-h- flex h-full flex-1 flex-col items-center justify-center gap-5">
      <img
        src="/no-trend-selection.svg"
        alt="Empty Folder"
        className="h-32.5 w-32.5"
      />
      <div className="flex flex-col items-center justify-center gap-2">
        <div className="text-xl font-bold text-neutral-900">{title}</div>
        <div className="text-sm text-neutral-700">{description}</div>
      </div>
    </div>
  );
}

function TrendViewGraph({
  id,
  hidden = false,
  type = 'absolute',
  parameterSelection,
  isLoading,
}: {
  hidden?: boolean;
  id?: string;
  type?: 'absolute' | 'percentage';
  parameterSelection?: any;
  isLoading?: boolean;
}) {
  if (!parameterSelection?.length) return null;

  const [chartType, setChartType] = useState<'absolute' | 'percentage'>('absolute');
  const [zoomState, setZoomState] = useState({
    left: 'dataMin' as string | number,
    right: 'dataMax' as string | number,
    top: 'dataMax+10' as string | number,
    bottom: 'dataMin-10' as string | number,
    refAreaLeft: '',
    refAreaRight: '',
  });

  const [brushRange, setBrushRange] = useState<{
    startIndex?: number;
    endIndex?: number;
  }>({});

  const chartData = useMemo(
    () => transformData(parameterSelection, chartType),
    [parameterSelection, chartType]
  );
  const chartConfig = useMemo(() => createChartConfig(parameterSelection), [parameterSelection]);
  const parameterIds = parameterSelection.map((p: ParameterSelection) => p.id);

  // Generate month ticks for equal interval display
  const allTimestamps = parameterSelection.length > 0 ? parameterSelection[0].timestamps : [];
  const monthTicks = generateMonthTicks(allTimestamps);

  const formatDateTime = (dateStr: string | number) => {
    const date = new Date(dateStr);
    return format(date, 'dd MMM yyyy');
  };

  const handleMouseDown = (e: CategoricalChartState) => {
    if (e && e.activeLabel) {
      setZoomState(
        (prev) =>
          ({
            ...prev,
            refAreaLeft: e.activeLabel,
            refAreaRight: '',
          }) as typeof zoomState
      );
    }
  };

  const handleMouseMove = (e: CategoricalChartState) => {
    if (zoomState.refAreaLeft && e && e.activeLabel) {
      setZoomState(
        (prev) =>
          ({
            ...prev,
            refAreaRight: e.activeLabel,
          }) as typeof zoomState
      );
    }
  };

  const handleMouseUp = () => {
    const {refAreaLeft, refAreaRight} = zoomState;
    if (!refAreaLeft || !refAreaRight || refAreaLeft === refAreaRight) {
      setZoomState((prev) => ({
        ...prev,
        refAreaLeft: '',
        refAreaRight: '',
      }));
      return;
    }

    let left = refAreaLeft;
    let right = refAreaRight;
    if (left > right) [left, right] = [right, left];

    const [bottom, top] = getAxisYDomain(chartData, left, right, parameterIds, 10);
    const {startIndex, endIndex} = findDataIndices(chartData, left, right);

    setZoomState({
      left,
      right,
      top,
      bottom,
      refAreaLeft: '',
      refAreaRight: '',
    });
    setBrushRange({
      startIndex,
      endIndex,
    });
  };

  const handleZoomOut = () => {
    setZoomState({
      left: 'dataMin',
      right: 'dataMax',
      top: 'dataMax+10',
      bottom: 'dataMin-10',
      refAreaLeft: '',
      refAreaRight: '',
    });
    setBrushRange({
      startIndex: 0,
      endIndex: chartData.length - 1,
    });
  };

  const onBrushDragEnd = (brushData: any) => {
    if (brushData && brushData.startIndex !== undefined && brushData.endIndex !== undefined) {
      console.log('brushData: ', brushData);
      const startTimestamp = chartData[brushData.startIndex]?.timestamp;
      const endTimestamp = chartData[brushData.endIndex]?.timestamp;

      console.log('chartData: ', chartData);
      console.log('startTimestamp: ', startTimestamp);
      console.log('endTimestamp: ', endTimestamp);

      // Update brush range state to maintain selection
      setBrushRange({
        startIndex: brushData.startIndex,
        endIndex: brushData.endIndex,
      });

      if (startTimestamp && endTimestamp) {
        const [bottom, top] = getAxisYDomain(chartData, startTimestamp, endTimestamp, parameterIds, 10);
        setZoomState({
          left: startTimestamp,
          right: endTimestamp,
          top,
          bottom,
          refAreaLeft: '',
          refAreaRight: '',
        });
      }
    }
  };

  useEffect(() => {
    if (parameterSelection.length > 1 && chartType === 'absolute') {
      setChartType('percentage');
    }
  }, [parameterSelection.length, chartType]);

  useEffect(() => {
    handleZoomOut();
    // Reset brush range when parameters change
    setBrushRange({});
  }, [parameterSelection]);

  return (
    <Card
      id={parameterSelection?.length ? id : undefined}
      className={clsx('relative h-full border-neutral-300', hidden && 'transparent h-120')}
    >
      {!!parameterSelection?.length ? (
        <>
          <CardHeader
            className={clsx(
              'flex flex-row items-center justify-between border-b border-neutral-300 px-4 pt-3 pb-1',
              hidden && 'hidden'
            )}
          >
            <div className="flex items-center gap-x-4">
              <CardTitle className="text-xs font-semibold text-neutral-800 uppercase">
                Lung Function Trend
              </CardTitle>
              <Button
                size="small"
                variant="outlined"
                onPress={handleZoomOut}
                className="px-3 py-1 text-xs"
                isDisabled={zoomState.left === 'dataMin' && zoomState.right === 'dataMax'}
              >
                Zoom out
              </Button>
            </div>

            <div className="mb-2 flex items-center justify-end">
              <div className="flex items-center gap-x-2 text-xs uppercase">
                <label
                  htmlFor="chart-type"
                  className={clsx(
                    'transition duration-75',
                    chartType === 'absolute' ? 'text-gray-600' : 'text-gray-400'
                  )}
                >
                  absolute value
                </label>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span>
                      <Switch
                        id="chart-type"
                        disabled={isLoading || (parameterSelection?.length > 1 && chartType === 'percentage')}
                        checked={chartType === 'percentage'}
                        onCheckedChange={() =>
                          setChartType(chartType === 'percentage' ? 'absolute' : 'percentage')
                        }
                      />
                    </span>
                  </TooltipTrigger>
                  <TooltipContent
                    className={parameterSelection?.length === 1 ? 'hidden' : 'max-w-md whitespace-pre-wrap'}
                  >
                    {parameterSelection?.length > 1 && chartType === 'percentage'
                      ? 'Cannot display % change for multiple parameters'
                      : ''}
                  </TooltipContent>
                </Tooltip>
                <label
                  htmlFor="chart-type"
                  className={clsx(
                    'transition duration-75',
                    chartType === 'percentage' ? 'text-gray-600' : 'text-gray-400'
                  )}
                >
                  % change
                </label>
              </div>
            </div>
          </CardHeader>
          <CardContent className="flex h-full flex-1 flex-col px-6">
            <div className="absolute top-15 left-4 text-xs text-neutral-500">
              Click and drag to zoom into a specific time range
            </div>
            <ChartContainer
              className="h-full flex-1"
              config={chartConfig}
              style={{userSelect: 'none'}}
            >
              <LineChart
                accessibilityLayer
                data={chartData}
                margin={{top: 40, right: 30, left: 15, bottom: 40}}
                onMouseDown={handleMouseDown}
                onMouseMove={handleMouseMove}
                onMouseUp={handleMouseUp}
              >
                <CartesianGrid
                  strokeDasharray="10 10"
                  stroke="var(--color-neutral-300)"
                  vertical={false}
                />
                <XAxis
                  style={{fill: 'var(--color-neutral-600)'}}
                  dataKey="timestamp"
                  tickLine={true}
                  axisLine={false}
                  tickMargin={30}
                  tickFormatter={formatDateTime}
                  height={100}
                  type="number"
                  scale="time"
                  domain={[zoomState.left, zoomState.right]}
                  ticks={monthTicks}
                  allowDataOverflow
                />
                <YAxis
                  style={{fill: 'var(--color-neutral-600)'}}
                  tickLine={false}
                  axisLine={false}
                  tickFormatter={(value) => (chartType === 'percentage' ? `${value}%` : value)}
                  label={{
                    value: chartType === 'percentage' ? '% Change (ref = earliest)' : 'Absolute Value',
                    angle: -90,
                    position: 'left',
                    offset: 10,
                    style: {textAnchor: 'middle', fill: 'var(--color-neutral-600)'},
                    className: 'text-secondary text-[9px] leading-[18px] uppercase',
                  }}
                  dx={-15}
                  // domain={[zoomState.bottom, zoomState.top]}
                  allowDataOverflow
                />
                <ChartTooltip
                  cursor={false}
                  content={
                    <ChartTooltipContent
                      titleKey="actualTestDate"
                      titleFormatter={(actualTestDate: string) => {
                        return formatDateTime(actualTestDate);
                      }}
                      valueFormatter={(value) => (chartType === 'percentage' ? `${value}%` : value)}
                    />
                  }
                />
                <Legend
                  align="right"
                  verticalAlign="top"
                  iconType="rect"
                  margin={{top: -200}}
                  height={40}
                />
                {parameterSelection.map((param: ParameterSelection) => (
                  <Line
                    key={param.id}
                    dataKey={param.id}
                    stroke={parameterColors[param.id] || '#000000'}
                    strokeWidth={2}
                    connectNulls={true}
                  >
                    <LabelList
                      dataKey={param.id}
                      offset={10}
                      content={
                        <CustomLabel
                          parameter={param}
                          type={type}
                        />
                      }
                    />
                  </Line>
                ))}

                {zoomState.refAreaLeft && zoomState.refAreaRight ? (
                  <ReferenceArea
                    x1={zoomState.refAreaLeft}
                    x2={zoomState.refAreaRight}
                    strokeOpacity={0.3}
                    fill="#dcebe9"
                    stroke="#4a827f"
                  />
                ) : (
                  <></>
                )}
                <Brush
                  dataKey="timestamp"
                  fill={'var(--color-brand2-200)'}
                  stroke={'var(--color-brand-500)'}
                  height={12}
                  startIndex={brushRange.startIndex}
                  endIndex={brushRange.endIndex}
                  onDragEnd={onBrushDragEnd}
                  tickFormatter={formatDateTime}
                  width={850}
                />
              </LineChart>
            </ChartContainer>
          </CardContent>
        </>
      ) : (
        <div className="h-full p-30">
          <EmptyState
            description="select a parameter to display trend"
            title="Select one or more parameter"
          />
        </div>
      )}
    </Card>
  );
}

export default TrendViewGraph;